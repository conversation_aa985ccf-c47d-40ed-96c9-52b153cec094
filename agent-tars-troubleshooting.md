# Agent TARS Troubleshooting Guide

## 🚨 Current Issue: Server Overload Error

**Error Message:**
```
Agent<PERSON><PERSON>ner [Stream] Error in agent loop execution: Error: The service is currently unable to handle additional requests due to server overload, please retry later or contact with administrator if not work afterwards. Request id: 021752721456646f91064fa121d196cc268d01819238f90468eec
```

**Root Cause:** Volcengine/Doubao API service is experiencing high load and rejecting requests.

## ✅ Solution: Switch to DeepSeek (Cost-Efficient Provider)

### 1. Get DeepSeek API Key
1. Visit: https://platform.deepseek.com/api_keys
2. Sign up/Login to your account
3. Generate a new API key
4. Copy the API key (starts with `sk-`)

### 2. Update Startup Script
The main startup script has been updated to use DeepSeek:

**File:** `start-agent-tars.sh`
```bash
#!/bin/bash

# 检查并终止占用8888端口的进程
echo "检查端口8888是否被占用..."
PORT_PID=$(lsof -ti :8888)
if [ ! -z "$PORT_PID" ]; then
    echo "发现端口8888被进程 $PORT_PID 占用，正在终止..."
    kill -9 $PORT_PID
    sleep 2
fi

export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
export NO_PROXY=localhost,127.0.0.1
export AGENT_TARS_DISABLE_AUTO_SEARCH=true

echo "启动Agent-TARS with DeepSeek (cost-efficient provider)..."
agent-tars --provider deepseek --model deepseek-chat --apiKey sk-your-deepseek-api-key-here
```

### 3. Replace API Key
Edit the startup script and replace `sk-your-deepseek-api-key-here` with your actual DeepSeek API key:

```bash
nano start-agent-tars.sh
# or
vim start-agent-tars.sh
```

### 4. Start Agent TARS
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
./start-agent-tars.sh
```

## 🔄 Alternative Providers

If DeepSeek also has issues, you can try these alternatives:

### Option 1: OpenAI (Most Reliable)
```bash
agent-tars --provider openai --model gpt-4o --apiKey sk-your-openai-key
```

### Option 2: Gemini (Google - Cost Efficient)
```bash
agent-tars --provider gemini --model gemini-1.5-pro --apiKey your-gemini-key
```

### Option 3: Local Ollama (Free but requires setup)
```bash
# First install and start Ollama
agent-tars --provider ollama --model llama3.1:8b
```

## 📁 Backup Files Created

- **Original Volcengine Script:** `start-agent-tars-volcengine-backup.sh`
- **Current DeepSeek Script:** `start-agent-tars.sh`

## 🔧 DeepSeek Configuration Details

- **Provider:** `deepseek`
- **Model:** `deepseek-chat` (fixed model name)
- **Base URL:** `https://api.deepseek.com/v1` (automatically configured)
- **API Key Format:** `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## 💰 Cost Comparison

| Provider | Cost (per 1M tokens) | Reliability | Speed |
|----------|---------------------|-------------|-------|
| DeepSeek | ~$0.14 | High | Fast |
| Volcengine | ~$0.50 | Medium (overloaded) | Fast |
| OpenAI GPT-4o | ~$5.00 | Very High | Fast |
| Gemini | ~$1.25 | High | Medium |

## 🚨 Common Issues & Solutions

### Issue 1: "Command not found: agent-tars"
**Solution:** Reinstall Agent TARS
```bash
npm install -g @agent-tars/cli
```

### Issue 2: Port 8888 already in use
**Solution:** The script automatically kills the process, but you can manually check:
```bash
lsof -i :8888
kill -9 <PID>
```

### Issue 3: API Key Invalid
**Solution:** 
1. Verify the API key is correct
2. Check if the key has proper permissions
3. Ensure no extra spaces in the key

### Issue 4: Network/Proxy Issues
**Solution:** The script includes proxy settings, but you can disable them:
```bash
# Comment out these lines in the script:
# export HTTP_PROXY=http://127.0.0.1:7890
# export HTTPS_PROXY=http://127.0.0.1:7890
```

## 📊 Chart Server Status

✅ **Chart Server Updated:** Now using official `@antv/mcp-server-chart`
- 25+ professional chart types available
- Charts will be properly visible and interactive
- No more HTML/PNG format issues

## 🔄 Quick Recovery Commands

### Restart with DeepSeek
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
./start-agent-tars.sh
```

### Fallback to Volcengine (if fixed)
```bash
./start-agent-tars-volcengine-backup.sh
```

### Check Agent TARS Status
```bash
ps aux | grep agent-tars
curl http://localhost:8888/health
```

## 📞 Next Steps

1. **Get DeepSeek API Key** from https://platform.deepseek.com/api_keys
2. **Update the startup script** with your API key
3. **Test the chart functionality** with the new setup
4. **Monitor performance** and switch providers if needed

The DeepSeek provider should resolve the server overload issues while providing cost-efficient AI capabilities!
