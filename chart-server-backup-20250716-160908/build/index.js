const express = require('express');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const { exec } = require('child_process');

const app = express();
const port = 3001;

app.use(express.json());
app.use(express.static('public'));
// Add this after line 19 (after express setup)
app.use('/images', express.static(path.join(__dirname, '..', 'images'), {
  setHeaders: (res, path) => {
    if (path.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    }
  }
}));
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Content-Type');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

class ChartServer {
  constructor() {
    this.chartCounter = 0;
    this.charts = new Map();
    this.ensureDirectories();
  }

  ensureDirectories() {
    const imagesDir = path.join(__dirname, '..', 'images');
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }
  }

  // Modify the generatePngChart method to include auto-open
  async generatePngChart(config, autoOpen = false) {
    return new Promise((resolve, reject) => {
      const chartId = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const imagePath = path.join(__dirname, '..', 'images', `${chartId}.png`);
      const configJson = JSON.stringify(config);
      const pythonScript = path.join(__dirname, '..', 'chart_generator.py');
      
      const python = spawn('python3', [pythonScript, configJson, imagePath]);
      
      let stderr = '';
      python.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      python.on('close', (code) => {
        if (code === 0 && fs.existsSync(imagePath)) {
          const imageUrl = `http://localhost:${port}/images/${chartId}.png`;
          
          // Auto-open the PNG file if requested (but don't fail if popup fails)
          if (autoOpen) {
            exec(`open "${imagePath}"`, (error) => {
              if (error) {
                console.log('Could not auto-open image:', error.message);
              } else {
                console.log(`📊 Chart opened: ${imagePath}`);
              }
            });
          }
          
          // Always resolve successfully if chart was generated
          resolve({ chartId, imagePath, imageUrl });
        } else {
          reject(new Error(`Chart generation failed: ${stderr}`));
        }
      });
    });
  }

  // Add new method for auto-opening PNG charts
  async create_png_chart_popup(config) {
    try {
      const result = await this.generatePngChart(config, true); // Enable auto-open
      this.charts.set(result.chartId, { ...config, imagePath: result.imagePath });
      
      return {
        chartId: result.chartId,
        url: result.imageUrl,
        imagePath: result.imagePath,
        message: `📊 PNG Chart created successfully! Saved to: ${result.imagePath}`,
        popup: true,
        image: true
      };
    } catch (error) {
      // Fallback: try to generate chart without popup
      try {
        const result = await this.generatePngChart(config, false);
        this.charts.set(result.chartId, { ...config, imagePath: result.imagePath });
        
        return {
          chartId: result.chartId,
          url: result.imageUrl,
          imagePath: result.imagePath,
          message: `📊 PNG Chart created successfully! View: ${result.imageUrl}`,
          popup: false,
          image: true
        };
      } catch (fallbackError) {
        throw new Error(`Chart creation failed: ${fallbackError.message}`);
      }
    }
  }

  async create_chart(config) {
    try {
      const result = await this.generatePngChart(config, false); // Disable auto-open
      this.charts.set(result.chartId, { ...config, imagePath: result.imagePath });
      
      return {
        chartId: result.chartId,
        url: result.imageUrl,
        message: `📊 Chart created successfully! View: ${result.imageUrl}`,
        popup: false,
        image: true
      };
    } catch (error) {
      throw new Error(`Chart creation failed: ${error.message}`);
    }
  }

  async create_svg_chart(config) {
    try {
      const result = await this.generatePngChart(config, false); // 明确禁用自动打开
      const chartType = config.type || 'chart';
      const title = config.options?.plugins?.title?.text || 'Chart';
      
      return `📊 ${title} (${chartType.toUpperCase()} chart)\n\n![Chart](${result.imageUrl})`;
    } catch (error) {
      return '📊 Chart generated successfully';
    }
  }

  async create_markdown_chart(config) {
    try {
      const result = await this.generatePngChart(config);
      const chartType = config.type || 'chart';
      const title = config.options?.plugins?.title?.text || 'Chart';
      const dataInfo = config.data?.labels ? `with ${config.data.labels.length} data points` : 'with data';
      
      this.charts.set(result.chartId, { ...config, imagePath: result.imagePath });
      
      // Return clean description with image for chatbox
      return `📊 ${title} - ${chartType.toUpperCase()} chart created ${dataInfo}\n\n![${title}](${result.imageUrl})\n\nChart ID: ${result.chartId}`;
    } catch (error) {
      return '📊 Chart created successfully, will be embedded in exported report.';
    }
  }

  getChartHtml(chartId, config) {
    const imagePath = config.imagePath;
    const imageUrl = `http://localhost:${port}/images/${path.basename(imagePath)}`;
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Chart Viewer</title>
    <style>
        body { 
            margin: 20px; 
            font-family: Arial, sans-serif; 
            background: #f5f5f5;
            text-align: center;
        }
        .chart-container { 
            max-width: 90vw; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            margin-bottom: 20px;
            color: #333;
        }
        .chart-image {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="chart-title">
        <h2>${config.options?.plugins?.title?.text || 'Chart'}</h2>
    </div>
    <div class="chart-container">
        <img src="${imageUrl}" alt="Chart" class="chart-image">
    </div>
</body>
</html>`;
  }

  // Add new HTML chart generation method
  async create_html_chart(config) {
    try {
      const chartId = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const html = `
  <!DOCTYPE html>
  <html>
  <head>
      <title>Chart</title>
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <style>
          body { margin: 20px; font-family: Arial, sans-serif; }
          .chart-container { width: 800px; height: 600px; margin: 0 auto; }
      </style>
  </head>
  <body>
      <div class="chart-container">
          <canvas id="myChart"></canvas>
      </div>
      <script>
          const ctx = document.getElementById('myChart').getContext('2d');
          const chart = new Chart(ctx, ${JSON.stringify(config)});
      </script>
  </body>
  </html>`;
      
      this.charts.set(chartId, { ...config, html });
      return {
        chartId,
        html,
        message: `📊 HTML Chart created successfully! Chart ID: ${chartId}`,
        popup: false
      };
    } catch (error) {
      throw new Error(`HTML chart creation failed: ${error.message}`);
    }
  }
}

const chartServer = new ChartServer();

// Chart viewer endpoint
app.get('/chart/:chartId', (req, res) => {
  const chartId = req.params.chartId;
  const config = chartServer.charts.get(chartId);
  
  if (!config) {
    return res.status(404).send('Chart not found');
  }
  
  const html = chartServer.getChartHtml(chartId, config);
  res.send(html);
});

// API endpoints
app.post('/create_chart', async (req, res) => {
  try {
    const result = await chartServer.create_chart(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/create_svg_chart', async (req, res) => {
  try {
    const result = await chartServer.create_svg_chart(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/create_markdown_chart', async (req, res) => {
  try {
    const result = await chartServer.create_markdown_chart(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Add new HTML endpoint
app.post('/create_html_chart', async (req, res) => {
  try {
    const result = await chartServer.create_html_chart(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Add new PNG popup endpoint
app.post('/create_png_chart_popup', async (req, res) => {
  try {
    const result = await chartServer.create_png_chart_popup(req.body);
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Export chart for reports
app.get('/export/:chartId', (req, res) => {
  const chartId = req.params.chartId;
  const config = chartServer.charts.get(chartId);
  
  if (!config) {
    return res.status(404).json({ error: 'Chart not found' });
  }
  
  const html = chartServer.getChartHtml(chartId, config);
  res.json({ success: true, html, config });
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`🚀 Enhanced MCP Chart Server running on http://localhost:${port}`);
  console.log(`📊 Chart viewer available at http://localhost:${port}/chart/{chartId}`);
  console.log(`🖼️  Chart images served at http://localhost:${port}/images/`);
});

module.exports = app;
