{"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "jonathan<PERSON>/ee-first", "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "files": ["index.js", "LICENSE"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}}