#!/bin/bash

# 检查并终止占用8888端口的进程
echo "检查端口8888是否被占用..."
PORT_PID=$(lsof -ti :8888)
if [ ! -z "$PORT_PID" ]; then
    echo "发现端口8888被进程 $PORT_PID 占用，正在终止..."
    kill -9 $PORT_PID
    sleep 2
fi

export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
export NO_PROXY=localhost,127.0.0.1
export AGENT_TARS_DISABLE_AUTO_SEARCH=true

echo "启动Agent-TARS with DeepSeek..."
echo "Get API key from: https://platform.deepseek.com/api_keys"
agent-tars --provider deepseek --model deepseek-chat --apiKey ***********************************
